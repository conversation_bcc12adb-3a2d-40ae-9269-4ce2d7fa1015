<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Process Modal 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-text {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            border-left: 4px solid #4285F4;
        }
        .test-button {
            background: #4285F4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #3367D6;
        }
        .instructions {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>AI Process Modal 测试页面</h1>
        
        <div class="instructions">
            <h3>测试说明</h3>
            <p>这个页面用于测试 AI Process Modal 组件的继续问答界面修改。</p>
            <ol>
                <li>选择下面的测试文本</li>
                <li>点击"测试继续问答"按钮</li>
                <li>查看新的水平布局界面：左侧返回按钮 + 右侧输入框</li>
                <li>测试返回按钮和回车键功能</li>
            </ol>
        </div>

        <div class="test-text">
            <p>这是一段测试文本，用于模拟用户选中的内容。请选择这段文字来测试AI助手的功能。</p>
            <p>改的党政风一些，让文字更加正式和官方。</p>
        </div>

        <button class="test-button" onclick="testContinueInput()">测试继续问答界面</button>
        <button class="test-button" onclick="testNormalFlow()">测试正常流程</button>

        <div id="modal-container"></div>
    </div>

    <script>
        // 模拟测试函数
        function testContinueInput() {
            alert('在实际的Chrome扩展环境中，这里会显示带有新水平布局的继续问答界面。\n\n新界面特点：\n- 左侧：← 返回按钮\n- 右侧：输入框\n- 支持回车键发送\n- 水平排列布局');
        }

        function testNormalFlow() {
            alert('这会触发正常的AI处理流程，完成后显示操作按钮，点击"继续问"会显示新的水平布局界面。');
        }

        // 模拟文本选择
        document.addEventListener('DOMContentLoaded', function() {
            const testText = document.querySelector('.test-text');
            testText.addEventListener('mouseup', function() {
                const selection = window.getSelection();
                if (selection.toString().length > 0) {
                    console.log('选中文本:', selection.toString());
                }
            });
        });
    </script>
</body>
</html>
